#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专业财务记账表格生成器
创建一个完整的Excel财务管理工作簿
"""

import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
from openpyxl.utils.dataframe import dataframe_to_rows
from openpyxl.worksheet.datavalidation import DataValidation
from datetime import datetime, timedelta
import calendar

def create_financial_workbook():
    """创建财务记账工作簿"""
    wb = Workbook()
    
    # 删除默认工作表
    wb.remove(wb.active)
    
    # 创建各个工作表
    create_summary_sheet(wb)
    create_income_sheet(wb)
    create_expense_sheet(wb)
    create_salary_sheet(wb)
    create_monthly_report_sheet(wb)
    
    # 保存文件
    wb.save("公司财务记账表.xlsx")
    print("✅ 财务记账表已成功创建：公司财务记账表.xlsx")

def get_style_config():
    """获取样式配置"""
    return {
        'header_font': Font(name='微软雅黑', size=12, bold=True, color='FFFFFF'),
        'header_fill': Pat<PERSON><PERSON>ill(start_color='4472C4', end_color='4472C4', fill_type='solid'),
        'data_font': Font(name='微软雅黑', size=10),
        'title_font': Font(name='微软雅黑', size=14, bold=True, color='2F5597'),
        'border': Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        ),
        'center_align': Alignment(horizontal='center', vertical='center'),
        'left_align': Alignment(horizontal='left', vertical='center')
    }

def apply_header_style(ws, row_num, col_start, col_end, styles):
    """应用表头样式"""
    for col in range(col_start, col_end + 1):
        cell = ws.cell(row=row_num, column=col)
        cell.font = styles['header_font']
        cell.fill = styles['header_fill']
        cell.border = styles['border']
        cell.alignment = styles['center_align']

def create_summary_sheet(wb):
    """创建汇总表"""
    ws = wb.create_sheet("财务汇总", 0)
    styles = get_style_config()
    
    # 设置标题
    ws['A1'] = "公司财务状况汇总表"
    ws['A1'].font = Font(name='微软雅黑', size=16, bold=True, color='2F5597')
    ws.merge_cells('A1:F1')
    ws['A1'].alignment = styles['center_align']
    
    # 当前月份信息
    current_date = datetime.now()
    ws['A3'] = f"统计月份：{current_date.year}年{current_date.month}月"
    ws['A3'].font = styles['title_font']
    
    # 收支概览
    ws['A5'] = "收支概览"
    ws['A5'].font = styles['title_font']
    
    headers = ['项目', '本月金额(元)', '上月金额(元)', '同比变化', '备注']
    for i, header in enumerate(headers, 1):
        ws.cell(row=6, column=i, value=header)
    apply_header_style(ws, 6, 1, 5, styles)
    
    # 收支数据行
    summary_data = [
        ['营业收入', '=营业收入!B:B的SUM', '0', '=B7-C7', ''],
        ['总支出', '=支出记录!C:C的SUM', '0', '=B8-C8', ''],
        ['员工工资', '=员工工资!F:F的SUM', '0', '=B9-C9', ''],
        ['净利润', '=B7-B8-B9', '0', '=B10-C10', '']
    ]
    
    for i, row_data in enumerate(summary_data, 7):
        for j, value in enumerate(row_data, 1):
            ws.cell(row=i, column=j, value=value)
            ws.cell(row=i, column=j).border = styles['border']
            ws.cell(row=i, column=j).alignment = styles['center_align']
    
    # 设置列宽
    column_widths = [15, 15, 15, 12, 20]
    for i, width in enumerate(column_widths, 1):
        ws.column_dimensions[chr(64 + i)].width = width

def create_income_sheet(wb):
    """创建营业收入表"""
    ws = wb.create_sheet("营业收入")
    styles = get_style_config()
    
    # 标题
    ws['A1'] = "营业收入记录表"
    ws['A1'].font = styles['title_font']
    ws.merge_cells('A1:F1')
    ws['A1'].alignment = styles['center_align']
    
    # 表头
    headers = ['序号', '日期', '收入来源', '收入金额(元)', '收款方式', '备注说明']
    for i, header in enumerate(headers, 1):
        ws.cell(row=3, column=i, value=header)
    apply_header_style(ws, 3, 1, 6, styles)
    
    # 示例数据
    sample_data = [
        [1, '2024-01-01', '产品销售', 50000, '银行转账', 'A产品销售收入'],
        [2, '2024-01-02', '服务费', 8000, '现金', '技术服务费'],
        [3, '2024-01-03', '咨询费', 12000, '支付宝', '管理咨询服务']
    ]
    
    for i, row_data in enumerate(sample_data, 4):
        for j, value in enumerate(row_data, 1):
            ws.cell(row=i, column=j, value=value)
            ws.cell(row=i, column=j).border = styles['border']
            ws.cell(row=i, column=j).alignment = styles['center_align']
    
    # 添加数据验证
    payment_validation = DataValidation(type="list", formula1='"银行转账,现金,支付宝,微信,支票"')
    ws.add_data_validation(payment_validation)
    payment_validation.add('E4:E1000')
    
    # 设置列宽
    column_widths = [8, 12, 20, 15, 12, 25]
    for i, width in enumerate(column_widths, 1):
        ws.column_dimensions[chr(64 + i)].width = width
    
    # 合计行
    total_row = 20
    ws.cell(row=total_row, column=3, value="本月收入合计:")
    ws.cell(row=total_row, column=4, value=f"=SUM(D4:D{total_row-1})")
    ws.cell(row=total_row, column=3).font = Font(bold=True)
    ws.cell(row=total_row, column=4).font = Font(bold=True)

def create_expense_sheet(wb):
    """创建支出记录表"""
    ws = wb.create_sheet("支出记录")
    styles = get_style_config()
    
    # 标题
    ws['A1'] = "支出记录表"
    ws['A1'].font = styles['title_font']
    ws.merge_cells('A1:G1')
    ws['A1'].alignment = styles['center_align']
    
    # 表头
    headers = ['序号', '日期', '支出类别', '支出金额(元)', '支付方式', '用途说明', '经办人']
    for i, header in enumerate(headers, 1):
        ws.cell(row=3, column=i, value=header)
    apply_header_style(ws, 3, 1, 7, styles)
    
    # 示例数据
    sample_data = [
        [1, '2024-01-01', '办公用品', 2500, '公司卡', '购买打印机、纸张等', '张三'],
        [2, '2024-01-02', '差旅费', 3200, '现金', '出差北京交通住宿费', '李四'],
        [3, '2024-01-03', '水电费', 1800, '银行转账', '办公室1月水电费', '王五']
    ]
    
    for i, row_data in enumerate(sample_data, 4):
        for j, value in enumerate(row_data, 1):
            ws.cell(row=i, column=j, value=value)
            ws.cell(row=i, column=j).border = styles['border']
            ws.cell(row=i, column=j).alignment = styles['center_align']
    
    # 添加支出类别验证
    category_validation = DataValidation(type="list", 
                                       formula1='"办公用品,差旅费,水电费,租金,通讯费,维修费,广告费,其他"')
    ws.add_data_validation(category_validation)
    category_validation.add('C4:C1000')
    
    # 添加支付方式验证
    payment_validation = DataValidation(type="list", 
                                      formula1='"公司卡,现金,银行转账,支付宝,微信,支票"')
    ws.add_data_validation(payment_validation)
    payment_validation.add('E4:E1000')
    
    # 设置列宽
    column_widths = [8, 12, 15, 15, 12, 25, 10]
    for i, width in enumerate(column_widths, 1):
        ws.column_dimensions[chr(64 + i)].width = width
    
    # 合计行
    total_row = 20
    ws.cell(row=total_row, column=3, value="本月支出合计:")
    ws.cell(row=total_row, column=4, value=f"=SUM(D4:D{total_row-1})")
    ws.cell(row=total_row, column=3).font = Font(bold=True)
    ws.cell(row=total_row, column=4).font = Font(bold=True)

def create_salary_sheet(wb):
    """创建员工工资表"""
    ws = wb.create_sheet("员工工资")
    styles = get_style_config()

    # 标题
    ws['A1'] = "员工工资管理表"
    ws['A1'].font = styles['title_font']
    ws.merge_cells('A1:H1')
    ws['A1'].alignment = styles['center_align']

    # 表头
    headers = ['序号', '员工姓名', '部门', '基本工资(元)', '绩效奖金(元)', '扣款(元)', '实发工资(元)', '发放日期']
    for i, header in enumerate(headers, 1):
        ws.cell(row=3, column=i, value=header)
    apply_header_style(ws, 3, 1, 8, styles)

    # 示例数据
    sample_data = [
        [1, '张三', '销售部', 8000, 2000, 200, '=D4+E4-F4', '2024-01-31'],
        [2, '李四', '技术部', 12000, 3000, 0, '=D5+E5-F5', '2024-01-31'],
        [3, '王五', '财务部', 9000, 1500, 100, '=D6+E6-F6', '2024-01-31'],
        [4, '赵六', '人事部', 7500, 1000, 0, '=D7+E7-F7', '2024-01-31']
    ]

    for i, row_data in enumerate(sample_data, 4):
        for j, value in enumerate(row_data, 1):
            ws.cell(row=i, column=j, value=value)
            ws.cell(row=i, column=j).border = styles['border']
            ws.cell(row=i, column=j).alignment = styles['center_align']

    # 添加部门验证
    dept_validation = DataValidation(type="list",
                                   formula1='"销售部,技术部,财务部,人事部,市场部,运营部,其他"')
    ws.add_data_validation(dept_validation)
    dept_validation.add('C4:C1000')

    # 设置列宽
    column_widths = [8, 12, 12, 15, 15, 12, 15, 12]
    for i, width in enumerate(column_widths, 1):
        ws.column_dimensions[chr(64 + i)].width = width

    # 合计行
    total_row = 15
    ws.cell(row=total_row, column=2, value="工资合计:")
    ws.cell(row=total_row, column=4, value=f"=SUM(D4:D{total_row-1})")
    ws.cell(row=total_row, column=5, value=f"=SUM(E4:E{total_row-1})")
    ws.cell(row=total_row, column=6, value=f"=SUM(F4:F{total_row-1})")
    ws.cell(row=total_row, column=7, value=f"=SUM(G4:G{total_row-1})")

    for col in [2, 4, 5, 6, 7]:
        ws.cell(row=total_row, column=col).font = Font(bold=True)
        ws.cell(row=total_row, column=col).border = styles['border']

def create_monthly_report_sheet(wb):
    """创建月度报表"""
    ws = wb.create_sheet("月度报表")
    styles = get_style_config()

    # 标题
    current_date = datetime.now()
    ws['A1'] = f"{current_date.year}年{current_date.month}月财务报表"
    ws['A1'].font = Font(name='微软雅黑', size=16, bold=True, color='2F5597')
    ws.merge_cells('A1:D1')
    ws['A1'].alignment = styles['center_align']

    # 收入分析
    ws['A3'] = "收入分析"
    ws['A3'].font = styles['title_font']

    income_headers = ['收入来源', '金额(元)', '占比(%)', '备注']
    for i, header in enumerate(income_headers, 1):
        ws.cell(row=4, column=i, value=header)
    apply_header_style(ws, 4, 1, 4, styles)

    # 支出分析
    ws['A10'] = "支出分析"
    ws['A10'].font = styles['title_font']

    expense_headers = ['支出类别', '金额(元)', '占比(%)', '备注']
    for i, header in enumerate(expense_headers, 1):
        ws.cell(row=11, column=i, value=header)
    apply_header_style(ws, 11, 1, 4, styles)

    # 财务指标
    ws['A17'] = "关键财务指标"
    ws['A17'].font = styles['title_font']

    kpi_data = [
        ['总收入', '=营业收入!D:D的SUM', '元'],
        ['总支出', '=支出记录!D:D的SUM', '元'],
        ['工资支出', '=员工工资!G:G的SUM', '元'],
        ['净利润', '=B18-B19-B20', '元'],
        ['利润率', '=B21/B18*100', '%']
    ]

    for i, row_data in enumerate(kpi_data, 18):
        for j, value in enumerate(row_data, 1):
            ws.cell(row=i, column=j, value=value)
            ws.cell(row=i, column=j).border = styles['border']
            if j == 1:
                ws.cell(row=i, column=j).font = Font(bold=True)

    # 设置列宽
    column_widths = [20, 15, 12, 20]
    for i, width in enumerate(column_widths, 1):
        ws.column_dimensions[chr(64 + i)].width = width

if __name__ == "__main__":
    try:
        create_financial_workbook()
    except ImportError as e:
        print("❌ 缺少必要的库，请先安装：")
        print("pip install pandas openpyxl")
    except Exception as e:
        print(f"❌ 创建表格时出错：{e}")
